import { ISummaryAPIProvider, SummaryAPIParams } from '../SummaryAPIProvider';

/**
 * 摘要 API 提供者实现
 * 
 * 负责纯 API 调用，不包含业务逻辑
 * 从原 SummaryService 迁移而来，遵循 DDD 基础设施层职责
 */
export class SummaryAPIProviderImpl implements ISummaryAPIProvider {
    /**
     * 调用 OpenAI API 生成摘要 (纯API调用)
     * @param params - API 调用参数
     * @returns API 响应的原始内容
     * @throws 如果 API 调用失败，则会抛出错误
     */
    async callSummaryAPI(params: SummaryAPIParams): Promise<string> {
        const { openai, model, prompt } = params;
        
        const completion = await openai.chat.completions.create({
            model: model,
            messages: [
                {
                    role: "user",
                    content: prompt,
                },
            ],
        });

        return completion.choices[0].message.content || "";
    }
}
