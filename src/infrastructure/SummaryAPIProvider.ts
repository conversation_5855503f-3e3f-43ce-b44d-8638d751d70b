import type { OpenAI } from "openai";

/**
 * 摘要 API 调用参数
 */
export interface SummaryAPIParams {
  /** OpenAI 实例 */
  openai: OpenAI;
  /** 使用的模型 */
  model: string;
  /** 完整的提示词 */
  prompt: string;
}

/**
 * 摘要 API 提供者接口
 * 
 * 负责定义摘要生成的 API 调用规范
 * 遵循基础设施层的职责：纯 API 调用，不包含业务逻辑
 */
export interface ISummaryAPIProvider {
  /**
   * 调用 OpenAI API 生成摘要
   * @param params - API 调用参数
   * @returns API 响应的原始内容
   * @throws 如果 API 调用失败，则会抛出错误
   */
  callSummaryAPI(params: SummaryAPIParams): Promise<string>;
}
